import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  ScrollView,
} from "react-native";
import shoeService from "../service/shoeService";

const ProductFormScreen = ({ navigation, route }) => {
  const { item } = route.params || {}; // nếu có item → đang edit
  const isEdit = !!item;

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    productCode: item?.productCode || "",
    productName: item?.productName || "",
    size: item?.size || "",
    price: item?.price?.toString() || "",
    quantity: item?.quantity?.toString() || "",
    image: item?.image || "https://via.placeholder.com/300",
  });

  const sizes = ["S", "M", "L", "XL", "XXL"];

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async () => {
    const { productCode, productName, size, price, quantity } = formData;
    if (!productCode || !productName || !size || !price || !quantity) {
      Alert.alert("Lỗi", "Vui lòng điền đầy đủ thông tin bắt buộc");
      return;
    }

    setLoading(true);
    try {
      let result;
      const dataToSend = {
        ...formData,
        price: Number(price),
        quantity: Number(quantity),
      };

      if (isEdit) {
        await shoeService.update(item._id, dataToSend);
        Alert.alert("Thành công", "Sửa sản phẩm thành công");
      } else {
        await shoeService.add(dataToSend);
        Alert.alert("Thành công", "Đã thêm sản phẩm");
      }

      navigation.navigate("Home");
    } catch (err) {
      Alert.alert("Lỗi", "Không thể lưu sản phẩm: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  const renderSizeButton = (label) => (
    <TouchableOpacity
      key={label}
      style={[
        styles.sizeButton,
        formData.size === label && { backgroundColor: "#4caf50" },
      ]}
      onPress={() => handleInputChange("size", label)}
    >
      <Text style={{ color: formData.size === label ? "#fff" : "#000" }}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text style={styles.title}>
        {isEdit ? "SỬA SẢN PHẨM" : "THÊM SẢN PHẨM"}
      </Text>

      <TextInput
        placeholder="Mã sản phẩm"
        value={formData.productCode}
        onChangeText={(text) => handleInputChange("productCode", text)}
        style={styles.input}
      />
      <TextInput
        placeholder="Tên sản phẩm"
        value={formData.productName}
        onChangeText={(text) => handleInputChange("productName", text)}
        style={styles.input}
      />

      <Text style={{ marginBottom: 4 }}>Chọn size</Text>
      <View style={styles.sizeRow}>{sizes.map(renderSizeButton)}</View>

      <TextInput
        placeholder="Giá (VND)"
        value={formData.price}
        onChangeText={(text) => handleInputChange("price", text)}
        keyboardType="numeric"
        style={styles.input}
      />
      <TextInput
        placeholder="Số lượng"
        value={formData.quantity}
        onChangeText={(text) => handleInputChange("quantity", text)}
        keyboardType="numeric"
        style={styles.input}
      />

      <TouchableOpacity
        style={[styles.submitButton, loading && styles.submitButtonDisabled]}
        onPress={handleSubmit}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="white" />
        ) : (
          <Text style={styles.submitButtonText}>
            {isEdit ? "LƯU" : "THÊM SẢN PHẨM"}
          </Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: "#fff",
    flexGrow: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 6,
    padding: 10,
    marginBottom: 12,
  },
  sizeRow: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 16,
  },
  sizeButton: {
    borderWidth: 1,
    borderColor: "#ccc",
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: "#f1f1f1",
    marginRight: 10,
    marginBottom: 10,
  },
  submitButton: {
    backgroundColor: "#4caf50",
    padding: 14,
    borderRadius: 6,
    alignItems: "center",
  },
  submitButtonDisabled: {
    backgroundColor: "#a5d6a7",
  },
  submitButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
});

export default ProductFormScreen;
