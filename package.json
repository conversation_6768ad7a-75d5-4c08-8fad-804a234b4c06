{"license": "0BSD", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.5", "@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.1.18", "@react-navigation/native-stack": "^7.3.27", "axios": "^1.12.2", "expo": "~53.0.22", "expo-asset": "~11.1.7", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.6", "react-native-paper": "4.9.2", "react-native-safe-area-context": "~5.6.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}