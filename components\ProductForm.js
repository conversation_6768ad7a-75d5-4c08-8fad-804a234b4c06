import { useEffect, useState, useRef, useCallback, useMemo } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from "react-native";
const API_BASE = "https://67da224335c87309f52b137a.mockapi.io/shoes";

const ProductForm = ({ initialData, onClose, onSubmitSuccess }) => {
  const [productCode, setProductCode] = useState(
    initialData?.productCode || ""
  );
  const [productName, setProductName] = useState(
    initialData?.productName || ""
  );
  const [size, setSize] = useState(initialData?.size || "");
  const [price, setPrice] = useState(initialData?.price?.toString() || "");
  const [quantity, setQuantity] = useState(
    initialData?.quantity?.toString() || ""
  );

  const isEdit = !!initialData;

  const handleSubmit = async () => {
    if (!productCode || !productName || !size || !price || !quantity) {
      Alert.alert("Vui lòng nhập đầy đủ thông tin");
      return;
    }

    const body = {
      productCode,
      productName,
      size,
      price: Number(price),
      quantity: Number(quantity),
      image: initialData?.image || "https://via.placeholder.com/300", // hoặc cho phép người dùng chọn ảnh
    };

    try {
      const res = await fetch(
        isEdit ? `${API_BASE}/${initialData.id}` : API_BASE,
        {
          method: isEdit ? "PUT" : "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(body),
        }
      );

      const result = await res.json();
      onSubmitSuccess(result);
    } catch (err) {
      Alert.alert("Lỗi", "Không thể lưu sản phẩm: " + err.message);
    }
  };

  const renderSizeButton = (label) => (
    <TouchableOpacity
      style={[
        styles.sizeButton,
        size === label && { backgroundColor: "#4caf50" },
      ]}
      onPress={() => setSize(label)}
    >
      <Text style={{ color: size === label ? "#fff" : "#000" }}>{label}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.form}>
      <TextInput
        placeholder="Mã sản phẩm"
        style={styles.input}
        value={productCode}
        onChangeText={setProductCode}
      />
      <TextInput
        placeholder="Tên sản phẩm"
        style={styles.input}
        value={productName}
        onChangeText={setProductName}
      />
      <Text style={{ marginBottom: 4 }}>Chọn size</Text>
      <View style={{ flexDirection: "row", gap: 8, marginBottom: 12 }}>
        {["S", "M", "L", "XL", "XXL"].map(renderSizeButton)}
      </View>
      <TextInput
        placeholder="Giá (VND)"
        style={styles.input}
        keyboardType="numeric"
        value={price}
        onChangeText={setPrice}
      />
      <TextInput
        placeholder="Số lượng"
        style={styles.input}
        keyboardType="numeric"
        value={quantity}
        onChangeText={setQuantity}
      />
      <View
        style={{ flexDirection: "row", justifyContent: "flex-end", gap: 10 }}
      >
        <TouchableOpacity style={styles.cancelBtn} onPress={onClose}>
          <Text style={{ color: "#fff" }}>Hủy</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.saveBtn} onPress={handleSubmit}>
          <Text style={{ color: "#fff" }}>{isEdit ? "Lưu" : "Thêm"}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: { flex: 1, padding: 16, backgroundColor: "#f5f5f5" },
  header: {
    paddingHorizontal: 0,
    paddingVertical: 20,
    borderRadius: 10,
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
  },
  linear: {
    opacity: 0.8,
    borderRadius: 10,
  },
  content: { flex: 1, marginTop: 30 },
  form: {
    marginBottom: 12,
    backgroundColor: "#fff",
    padding: 12,
    borderRadius: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    padding: 8,
    borderRadius: 6,
    marginBottom: 8,
  },
  inputInline: {
    borderWidth: 1,
    borderColor: "#ddd",
    padding: 8,
    borderRadius: 6,
    marginBottom: 6,
  },
  addBtn: {
    backgroundColor: "green",
    marginVertical: 20,
    padding: 12,
    borderRadius: 6,
    alignItems: "center",
  },
  addBtnText: { color: "#fff", fontWeight: "600" },
  item: {
    backgroundColor: "#fff",
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    flexDirection: "column",
  },
  itemRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },

  actionRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    gap: 8,
  },
  card: {
    backgroundColor: "#fff",
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },

  code: {
    color: "#3f51b5",
    fontWeight: "700",
  },

  productName: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 6,
  },

  price: {
    color: "green",
    fontWeight: "bold",
    fontSize: 16,
    marginBottom: 4,
  },

  quantity: {
    color: "#666",
    marginBottom: 8,
  },

  sizeTag: {
    backgroundColor: "#f1f1f1",
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },

  sizeText: {
    fontSize: 12,
    color: "#555",
  },

  actionButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 8,
  },

  editBtn: {
    backgroundColor: "#ffca28",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
  },

  deleteBtn: {
    backgroundColor: "#e53935",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
  },

  btnText: {
    color: "#fff",
    fontWeight: "600",
  },

  saveBtn: {
    backgroundColor: "#4caf50",
    padding: 8,
    borderRadius: 6,
    marginRight: 8,
  },
  cancelBtn: { backgroundColor: "#9e9e9e", padding: 8, borderRadius: 6 },
  loadingOverlay: {
    position: "absolute",
    left: 0,
    right: 0,
    top: "50%",
    alignItems: "center",
  },

  empty: { alignItems: "center", marginTop: 40 },
  sizeButton: {
    borderWidth: 1,
    borderColor: "#ccc",
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: "#f1f1f1",
  },
});
export default ProductForm;
