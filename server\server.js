const express = require("express");
const mongoose = require("mongoose");
const bodyParser = require("body-parser");
const cors = require("cors");

const app = express();
app.use(cors());
app.use(bodyParser.json());

// Kết nối MongoDB local
mongoose.connect("mongodb://127.0.0.1:27017/mobile");

// Schema
const shoeSchema = new mongoose.Schema({
  productCode: String,
  productName: String,
  size: String,
  price: String,
  quantity: Number,
  image: String,
  description: String,
});

const Shoe = mongoose.model("Shoe", shoeSchema);

// ---------------- API ----------------
// GET ALL
app.get("/shoes", async (req, res) => {
  const list = await Shoe.find();
  res.send(list);
});

// GET BY ID
app.get("/shoes/:id", async (req, res) => {
  const shoe = await Shoe.findById(req.params.id);
  res.send(shoe);
});

// POST
app.post("/shoes", async (req, res) => {
  const newShoe = new Shoe(req.body);
  await newShoe.save();
  res.send(newShoe);
});

// PUT
app.put("/shoes/:id", async (req, res) => {
  const updated = await Shoe.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
  });
  res.send(updated);
});

// DELETE
app.delete("/shoes/:id", async (req, res) => {
  const deleted = await Shoe.findByIdAndDelete(req.params.id);
  res.send(deleted);
});

app.listen(3000, "0.0.0.0", () => {
  console.log("✅ Server running on port 3000");
});
