import { useEffect, useState, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Image,
  Platform,
} from "react-native";
import { useFocusEffect, useRoute } from "@react-navigation/native";
import shoeService from "../service/shoeService";

export default function HomeScreen({ navigation }) {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const route = useRoute();

  useFocusEffect(
    useCallback(() => {
      fetchProducts(); // ✅ sẽ tự reload danh sách khi quay về
    }, [])
  );

  useEffect(() => {
    if (route.params?.updatedProduct) {
      if (route.params.isEdit) {
        setProducts((prev) =>
          prev.map((p) =>
            p._id === route.params.updatedProduct._id
              ? route.params.updatedProduct
              : p
          )
        );
      } else {
        setProducts((prev) => [route.params.updatedProduct, ...prev]);
      }
    }
  }, [route.params?.updatedProduct]);

  const formatCurrency = (num) =>
    new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(num);

  // Lấy danh sách sản phẩm
  const fetchProducts = async () => {
    try {
      if (!refreshing) setLoading(true);
      const data = await shoeService.list();
      setProducts(data.reverse()); // hiển thị mới nhất lên đầu
    } catch (err) {
      Alert.alert("Lỗi", "Không thể tải danh sách sản phẩm");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Tự động load lại mỗi khi màn hình focus
  useFocusEffect(
    useCallback(() => {
      fetchProducts();
    }, [])
  );

  // Nhận dữ liệu trả về từ form sau khi thêm/sửa
  useEffect(() => {
    if (route.params?.updatedProduct) {
      const updated = route.params.updatedProduct;
      const isEdit = route.params.isEdit;

      setProducts((prev) => {
        if (isEdit) {
          return prev.map((item) => (item.id === updated.id ? updated : item));
        } else {
          return [updated, ...prev];
        }
      });
    }
  }, [route.params?.updatedProduct]);

  // Xử lý xóa sản phẩm
  const handleDelete = async (id) => {
    if (Platform.OS === "web") {
      // Xác nhận xóa trên web
      const confirmDelete = window.confirm(
        "Bạn có chắc muốn xóa sản phẩm này?"
      );
      if (!confirmDelete) return;
    }

    try {
      await shoeService.delete(id);
      Alert.alert("Thành công", "Đã xóa sản phẩm");
      fetchProducts();
    } catch (error) {
      Alert.alert("Lỗi", "Không thể xóa sản phẩm");
      console.error("Delete error:", error);
    }
  };

  // Hiển thị từng sản phẩm
  const renderItem = ({ item }) => (
    <View
      style={{
        backgroundColor: "#fff",
        padding: 12,
        marginBottom: 12,
        borderRadius: 8,
        elevation: 3,
      }}
    >
      <Image
        source={{
          uri:
            item.image ||
            "https://via.placeholder.com/300x150.png?text=No+Image",
        }}
        style={{ height: 150, borderRadius: 6, marginBottom: 8 }}
      />
      <Text style={{ fontWeight: "bold", fontSize: 16 }}>
        {item.productName}
      </Text>
      <Text>Giá: {formatCurrency(item.price)}</Text>
      <Text>Số lượng: {item.quantity}</Text>
      <Text>Size: {item.size}</Text>

      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          marginTop: 10,
        }}
      >
        <TouchableOpacity
          style={{ backgroundColor: "orange", padding: 8, borderRadius: 4 }}
          onPress={() => navigation.navigate("ProductForm", { item })}
        >
          <Text style={{ color: "#fff" }}>Sửa</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{ backgroundColor: "red", padding: 8, borderRadius: 4 }}
          onPress={() => handleDelete(item._id)}
        >
          <Text style={{ color: "#fff" }}>Xóa</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, padding: 16, backgroundColor: "#f0f0f0" }}>
      <TouchableOpacity
        style={{
          backgroundColor: "green",
          padding: 12,
          marginBottom: 16,
          borderRadius: 6,
          alignItems: "center",
        }}
        onPress={() => navigation.navigate("ProductForm")}
      >
        <Text style={{ color: "#fff", fontWeight: "bold", fontSize: 16 }}>
          THÊM/SỬA SẢN PHẨM
        </Text>
      </TouchableOpacity>

      {loading ? (
        <ActivityIndicator
          size="large"
          color="green"
          style={{ marginTop: 30 }}
        />
      ) : (
        <FlatList
          data={products}
          keyExtractor={(item) => item._id}
          renderItem={renderItem}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={() => {
                setRefreshing(true);
                fetchProducts();
              }}
            />
          }
          ListEmptyComponent={() => (
            <Text style={{ textAlign: "center", marginTop: 40 }}>
              Không có sản phẩm
            </Text>
          )}
        />
      )}
    </View>
  );
}
