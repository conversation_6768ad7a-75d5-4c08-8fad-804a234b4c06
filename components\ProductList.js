// ProductList.js
import React from "react";
import { FlatList, RefreshControl, View, Text, StyleSheet } from "react-native";
import ProductCard from "./ProductCard";

export default function ProductList({
  products,
  refreshing,
  handleRefresh,
  editId,
  editName,
  editPrice,
  setEditName,
  setEditPrice,
  onStartEdit,
  saveEdit,
  cancelEdit,
  handleDelete,
  loading,
}) {
  return (
    <FlatList
      data={products}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <ProductCard
          item={item}
          editId={editId}
          editName={editName}
          editPrice={editPrice}
          setEditName={setEditName}
          setEditPrice={setEditPrice}
          onStartEdit={onStartEdit}
          saveEdit={saveEdit}
          cancelEdit={cancelEdit}
          handleDelete={handleDelete}
        />
      )}
      contentContainerStyle={{ paddingBottom: 40 }}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
      ListEmptyComponent={() =>
        !loading && (
          <View style={styles.empty}>
            <Text>Không có sản phẩm</Text>
          </View>
        )
      }
    />
  );
}

const styles = StyleSheet.create({
  empty: { alignItems: "center", marginTop: 40 },
});
