import axios from "axios";

const MOCK_URL = "https://67da224335c87309f52b137a.mockapi.io/s";
const MONGO_URL = "http://localhost:3000";

const api = axios.create({
  baseURL: MONGO_URL,
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 10000,
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error("API Error:", error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export default {
  // L<PERSON>y danh sách sản phẩm
  list: async () => {
    try {
      const res = await api.get("/shoes");
      return res.data;
    } catch (error) {
      console.error("List error:", error);
      throw error;
    }
  },

  // Thêm mới sản phẩm
  add: async (product) => {
    try {
      const res = await api.post("/shoes", product);
      return res.data;
    } catch (error) {
      console.error("Add error:", error);
      throw error;
    }
  },

  // Cập nhật sản phẩm theo ID
  update: async (id, updatedProduct) => {
    try {
      const res = await api.put(`/shoes/${id}`, updatedProduct);
      return res.data;
    } catch (error) {
      console.error("Update error:", error);
      throw error;
    }
  },

  // Xóa sản phẩm theo ID
  delete: async (id) => {
    try {
      const res = await api.delete(`/shoes/${id}`);
      return res.data;
    } catch (error) {
      console.error("Delete error:", error);
      throw error;
    }
  },
};
