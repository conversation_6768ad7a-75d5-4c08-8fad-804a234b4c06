// ProductCard.js
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from "react-native";

export default function ProductCard({
  item,
  editId,
  editName,
  editPrice,
  setEditName,
  setEditPrice,
  onStartEdit,
  saveEdit,
  cancelEdit,
  handleDelete,
}) {
  const isEditing = item.id === editId;

  return (
    <View style={styles.item}>
      {isEditing ? (
        <View>
          <TextInput
            value={editName}
            onChangeText={setEditName}
            placeholder="Tên sản phẩm"
            style={styles.inputInline}
          />
          <TextInput
            value={editPrice}
            onChangeText={setEditPrice}
            placeholder="Giá"
            keyboardType="numeric"
            style={styles.inputInline}
          />
          <View style={styles.actionRow}>
            <TouchableOpacity onPress={saveEdit} style={styles.saveBtn}>
              <Text style={styles.btnText}>Save</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={cancelEdit} style={styles.cancelBtn}>
              <Text style={styles.btnText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.itemRow}>
          <View style={{ flex: 1 }}>
            <Text style={styles.title}>{item.name}</Text>
            <Text style={styles.price}>₫ {item.price}</Text>
          </View>
          <View style={styles.actionRow}>
            <TouchableOpacity
              onPress={() => onStartEdit(item)}
              style={styles.editBtn}
            >
              <Text style={styles.btnText}>Edit</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => handleDelete(item.id)}
              style={styles.deleteBtn}
            >
              <Text style={styles.btnText}>Delete</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  item: {
    backgroundColor: "#fff",
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    flexDirection: "column",
  },
  itemRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  title: { fontSize: 16, fontWeight: "600" },
  price: { marginTop: 4, color: "#333" },
  inputInline: {
    borderWidth: 1,
    borderColor: "#ddd",
    padding: 8,
    borderRadius: 6,
    marginBottom: 6,
  },
  actionRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    gap: 8,
  },
  editBtn: {
    backgroundColor: "#ffd54f",
    padding: 8,
    borderRadius: 6,
  },
  deleteBtn: {
    backgroundColor: "#e57373",
    padding: 8,
    borderRadius: 6,
  },
  saveBtn: {
    backgroundColor: "#4caf50",
    padding: 8,
    borderRadius: 6,
  },
  cancelBtn: { backgroundColor: "#9e9e9e", padding: 8, borderRadius: 6 },
  btnText: { color: "#fff", fontWeight: "600" },
});
